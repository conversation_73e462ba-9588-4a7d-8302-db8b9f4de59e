<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <div class="py-6">
    <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
    
    <!-- Stats Grid -->
    <div class="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <span class="text-white font-semibold">U</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @user_count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <span class="text-white font-semibold">O</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Organizations</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @organization_count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                <span class="text-white font-semibold">J</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Jobs</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @job_count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                <span class="text-white font-semibold">T</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Talent Profiles</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @talent_profile_count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
      <!-- Recent Users -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Users</h3>
          <div class="mt-5">
            <div class="flow-root">
              <ul class="-my-5 divide-y divide-gray-200">
                <% @recent_users.each do |user| %>
                  <li class="py-4">
                    <div class="flex items-center space-x-4">
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">
                          <%= user.full_name.presence || user.email %>
                        </p>
                        <p class="text-sm text-gray-500 truncate">
                          <%= user.email %>
                        </p>
                      </div>
                      <div class="flex-shrink-0 text-sm text-gray-500">
                        <%= time_ago_in_words(user.created_at) %> ago
                      </div>
                    </div>
                  </li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Jobs -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Jobs</h3>
          <div class="mt-5">
            <div class="flow-root">
              <ul class="-my-5 divide-y divide-gray-200">
                <% @recent_jobs.each do |job| %>
                  <li class="py-4">
                    <div class="flex items-center space-x-4">
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">
                          <%= job.title %>
                        </p>
                        <p class="text-sm text-gray-500 truncate">
                          <%= job.job_category&.humanize %>
                        </p>
                      </div>
                      <div class="flex-shrink-0 text-sm text-gray-500">
                        <%= time_ago_in_words(job.created_at) %> ago
                      </div>
                    </div>
                  </li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8">
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Quick Actions</h3>
          <div class="mt-5 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <%= link_to madmin_users_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700" do %>
              Manage Users
            <% end %>
            <%= link_to madmin_organizations_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700" do %>
              Manage Organizations
            <% end %>
            <%= link_to madmin_jobs_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700" do %>
              Manage Jobs
            <% end %>
            <%= link_to madmin_roles_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700" do %>
              Manage Roles
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
