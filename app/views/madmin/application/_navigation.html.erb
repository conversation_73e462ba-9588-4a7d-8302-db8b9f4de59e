<%# Custom madmin navigation override for namespace compatibility %>
<h1>
  <%= link_to super_admin_madmin_users_path do %>
    <%= Madmin.site_name || "Admin" %>
  <% end %>
</h1>

<nav>
  <ul>
    <% Madmin.menu.render do |item| %>
      <% if item.url %>
        <li>
          <%= link_to item.label, item.url, class: ("active" if current_page?(item.url)) %>
        </li>
      <% else %>
        <li class="nav-header">
          <h3><%= item.label %></h3>
        </li>
      <% end %>
    <% end %>
  </ul>
</nav>
