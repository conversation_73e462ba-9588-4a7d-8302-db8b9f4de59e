module Madmin
  class ApplicationController < Madmin::BaseController
    include Rails.application.routes.url_helpers

    before_action :set_current_request_details
    before_action :authenticate
    before_action :check_verification
    before_action :require_onboarding_completion
    before_action :set_current_organization
    before_action :require_organization_selected
    before_action :authenticate_admin_user

    private

    def authenticate_admin_user
      unless Current.user&.superadmin?
        redirect_to root_path,
                    alert: 'Access denied. Super admin privileges required.'
      end
    end

    def current_user
      Current.user
    end

    # Include the same authentication methods as ApplicationController
    def authenticate
      if session_record = Session.find_by_id(cookies.signed[:session_token])
        Current.session = session_record
        Current.user = session_record.user

        # Set impersonation attributes from session if available
        if session[:impersonator_id] && session[:impersonation_log_id]
          Current.impersonator_id = session[:impersonator_id]
          Current.impersonation_log_id = session[:impersonation_log_id]
        end
      else
        redirect_to sign_in_path
      end
    end

    def set_current_request_details
      Current.user_agent = request.user_agent
      Current.ip_address = request.ip
    end

    def check_verification
      return unless Current.user
      return if Current.user.verified?

      redirect_to identity_email_verification_path,
                  alert: 'Please verify your email address before continuing.'
    end

    def require_onboarding_completion
      return unless Current.user
      return if Current.user.onboarding_completed?

      redirect_to(
        if Current.user.onboarding_step == 'personal'
          onboarding_personal_path
        else
          onboarding_organization_path
        end,
        alert:
          'Please complete your onboarding before accessing admin features.',
      )
    end

    def set_current_organization
      return unless Current.user

      if session[:organization_id]
        Current.organization =
          Current.user.organizations.find_by(id: session[:organization_id])
        session.delete(:organization_id) unless Current.organization
      end

      if Current.organization.nil? && Current.user.organizations.count == 1
        org = Current.user.organizations.first
        Current.organization = org
        session[:organization_id] = org.id
      end
    end

    def require_organization_selected
      return unless Current.user
      return if Current.organization

      redirect_to organizations_path,
                  alert: 'Please select an organization to continue.'
    end
  end
end
