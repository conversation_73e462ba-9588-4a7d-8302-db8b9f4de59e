module Madmin
  class DashboardController < ApplicationController
    def show
      @user_count = User.count
      @organization_count = Organization.count
      @job_count = Job.count
      @talent_profile_count = TalentProfile.count
      @recent_users = User.order(created_at: :desc).limit(5)
      @recent_jobs = Job.order(created_at: :desc).limit(5)
      @active_sessions = Session.joins(:user).order(created_at: :desc).limit(10)
    end
  end
end
