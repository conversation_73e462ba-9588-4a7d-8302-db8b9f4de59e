class OrganizationResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :name
  attribute :created_at, form: false
  attribute :updated_at, form: false
  attribute :operating_timezone
  attribute :size
  attribute :logo, index: false

  # Associations
  attribute :jobs
  attribute :organization_memberships
  attribute :users

  # Add scopes to easily filter records by size
  scope :small, -> { where(size: 'small') }
  scope :medium, -> { where(size: 'medium') }
  scope :large, -> { where(size: 'large') }
  scope :enterprise, -> { where(size: 'enterprise') }

  # Add actions to the resource's show page
  member_action do |record|
    link_to "View Jobs (#{record.jobs.count})", madmin_jobs_path(organization_id: record.id),
            class: "btn btn-info"
  end

  member_action do |record|
    link_to "View Members (#{record.users.count})", madmin_organization_memberships_path(organization_id: record.id),
            class: "btn btn-secondary"
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record) = record.name

  # Customize the default sort column and direction.
  def self.default_sort_column = "created_at"
  def self.default_sort_direction = "desc"
end
