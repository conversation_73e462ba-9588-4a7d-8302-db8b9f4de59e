class UserResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :email
  attribute :first_name
  attribute :last_name
  attribute :verified
  attribute :onboarding_completed
  attribute :scout_signup_completed
  attribute :talent_signup_completed
  attribute :signup_intent
  attribute :onboarding_step
  attribute :time_zone
  attribute :verification_email_sent_at, form: false
  attribute :last_logged_in_organization_id, form: false
  attribute :created_at, form: false
  attribute :updated_at, form: false
  attribute :password, index: false, show: false
  attribute :password_confirmation, index: false, show: false
  attribute :avatar, index: false

  # Associations
  attribute :roles
  attribute :user_roles
  attribute :organizations
  attribute :organization_memberships
  attribute :talent_profile
  attribute :sessions
  attribute :job_applications
  attribute :job_invitations
  attribute :saved_jobs
  attribute :conversations
  attribute :messages
  attribute :sent_chat_requests
  attribute :received_chat_requests
  attribute :talent_bookmarks
  attribute :pay_customers, show: false
  attribute :charges, show: false
  attribute :subscriptions, show: false

  # Add scopes to easily filter records
  scope :verified, -> { where(verified: true) }
  scope :unverified, -> { where(verified: false) }
  scope :onboarding_completed, -> { where(onboarding_completed: true) }
  scope :onboarding_pending, -> { where(onboarding_completed: false) }
  scope :scouts, -> { where(scout_signup_completed: true) }
  scope :talents, -> { where(talent_signup_completed: true) }

  # Add actions to the resource's show page
  member_action do |record|
    if record.verified?
      link_to "Unverify User", "#", class: "btn btn-warning"
    else
      link_to "Verify User", "#", class: "btn btn-success"
    end
  end

  member_action do |record|
    link_to "Impersonate", super_admin_masquerades_path(user_id: record.id),
            method: :post, class: "btn btn-info"
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record)
    record.full_name.presence || record.email
  end

  # Customize the default sort column and direction.
  def self.default_sort_column = "created_at"
  def self.default_sort_direction = "desc"
end
