class ImpersonationLogResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :started_at
  attribute :ended_at
  attribute :ip_address
  attribute :user_agent
  attribute :created_at, form: false
  attribute :updated_at, form: false

  # Associations
  attribute :admin
  attribute :user

  # Add scopes to easily filter records
  scope :active, -> { where(ended_at: nil) }
  scope :completed, -> { where.not(ended_at: nil) }
  scope :recent, -> { where('started_at > ?', 7.days.ago) }
  scope :long_sessions, -> { where('ended_at - started_at > ?', 1.hour) }

  # Add actions to the resource's show page
  member_action do |record|
    link_to "View Admin", madmin_user_path(record.admin), class: "btn btn-info"
  end

  member_action do |record|
    link_to "View Impersonated User", madmin_user_path(record.user),
            class: "btn btn-secondary"
  end

  member_action do |record|
    if record.ended_at.nil?
      link_to "End Impersonation", "#", class: "btn btn-warning",
              data: { confirm: "Are you sure you want to end this impersonation session?" }
    end
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record)
    admin_name = record.admin.full_name || record.admin.email
    user_name = record.user.full_name || record.user.email
    status = record.ended_at ? "Ended" : "Active"
    "#{admin_name} → #{user_name} (#{status})"
  end

  # Customize the default sort column and direction.
  def self.default_sort_column = "started_at"
  def self.default_sort_direction = "desc"
end
