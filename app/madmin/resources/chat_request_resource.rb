class ChatRequestResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :status
  attribute :requested_at
  attribute :accepted_at
  attribute :declined_at
  attribute :created_at, form: false
  attribute :updated_at, form: false
  attribute :pitch

  # Associations
  attribute :scout
  attribute :talent

  # Add scopes to easily filter records
  scope :pending, -> { where(status: 'pending') }
  scope :accepted, -> { where(status: 'accepted') }
  scope :declined, -> { where(status: 'declined') }
  scope :recent, -> { where('requested_at > ?', 7.days.ago) }

  # Add actions to the resource's show page
  member_action do |record|
    link_to "View Scout", madmin_user_path(record.scout), class: "btn btn-info"
  end

  member_action do |record|
    link_to "View Talent", madmin_user_path(record.talent), class: "btn btn-secondary"
  end

  member_action do |record|
    if record.talent.talent_profile
      link_to "View Talent Profile", madmin_talent_profile_path(record.talent.talent_profile),
              class: "btn btn-outline-info"
    end
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record)
    scout_name = record.scout.full_name || record.scout.email
    talent_name = record.talent.full_name || record.talent.email
    "#{scout_name} → #{talent_name} (#{record.status})"
  end

  # Customize the default sort column and direction.
  def self.default_sort_column = "requested_at"
  def self.default_sort_direction = "desc"
end
