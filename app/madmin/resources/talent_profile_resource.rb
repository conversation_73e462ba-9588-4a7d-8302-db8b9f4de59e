class TalentProfileResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :bio
  attribute :looking_for
  attribute :skills
  attribute :about
  attribute :vsl_link
  attribute :availability_status
  attribute :price_range_min
  attribute :price_range_max
  attribute :pricing_model
  attribute :portfolio_link
  attribute :linkedin_url
  attribute :x_url
  attribute :website_url
  attribute :achievement_badges
  attribute :platform_choice
  attribute :location
  attribute :instagram_url
  attribute :threads_url
  attribute :niches
  attribute :outcomes
  attribute :ghostwriter_type
  attribute :social_media_specialty
  attribute :created_at, form: false
  attribute :updated_at, form: false
  attribute :headline
  attribute :is_agency
  attribute :location_preference
  attribute :is_premium
  attribute :attachments, index: false

  # Associations
  attribute :user
  attribute :talent_bookmarks
  attribute :bookmarked_by_users
  attribute :talent_notes

  # Add scopes to easily filter records
  scope :available, -> { where(availability_status: 'available') }
  scope :busy, -> { where(availability_status: 'busy') }
  scope :unavailable, -> { where(availability_status: 'unavailable') }
  scope :premium, -> { where(is_premium: true) }
  scope :agencies, -> { where(is_agency: true) }
  scope :freelancers, -> { where(is_agency: false) }
  scope :newsletter_writers, -> { where(ghostwriter_type: 'newsletter') }
  scope :social_media_writers, -> { where(ghostwriter_type: 'social_media') }

  # Add actions to the resource's show page
  member_action do |record|
    link_to "View User", madmin_user_path(record.user), class: "btn btn-info"
  end

  member_action do |record|
    link_to "View Bookmarks (#{record.talent_bookmarks.count})",
            madmin_talent_bookmarks_path(talent_profile_id: record.id),
            class: "btn btn-secondary"
  end

  member_action do |record|
    if record.is_premium?
      link_to "Remove Premium", "#", class: "btn btn-warning"
    else
      link_to "Make Premium", "#", class: "btn btn-success"
    end
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record)
    record.user.full_name.presence || record.user.email
  end

  # Customize the default sort column and direction.
  def self.default_sort_column = "created_at"
  def self.default_sort_direction = "desc"
end
