class MessageResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :body
  attribute :read_at
  attribute :created_at, form: false
  attribute :updated_at, form: false

  # Associations
  attribute :conversation
  attribute :user

  # Add scopes to easily filter records
  scope :read, -> { where.not(read_at: nil) }
  scope :unread, -> { where(read_at: nil) }
  scope :recent, -> { where('created_at > ?', 24.hours.ago) }

  # Add actions to the resource's show page
  member_action do |record|
    link_to "View Conversation", madmin_conversation_path(record.conversation),
            class: "btn btn-info"
  end

  member_action do |record|
    link_to "View Sender", madmin_user_path(record.user), class: "btn btn-secondary"
  end

  member_action do |record|
    if record.read_at.nil?
      link_to "Mark as Read", "#", class: "btn btn-success"
    else
      link_to "Mark as Unread", "#", class: "btn btn-warning"
    end
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record)
    sender = record.user.full_name || record.user.email
    preview = record.body.truncate(50)
    "#{sender}: #{preview}"
  end

  # Customize the default sort column and direction.
  def self.default_sort_column = "created_at"
  def self.default_sort_direction = "desc"
end
