class OrganizationMembershipResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :org_role
  attribute :created_at, form: false
  attribute :updated_at, form: false

  # Associations
  attribute :user
  attribute :organization

  # Add scopes to easily filter records by role
  scope :owners, -> { where(org_role: 'owner') }
  scope :admins, -> { where(org_role: 'admin') }
  scope :members, -> { where(org_role: 'member') }

  # Add actions to the resource's show page
  member_action do |record|
    link_to "View User", madmin_user_path(record.user), class: "btn btn-info"
  end

  member_action do |record|
    link_to "View Organization", madmin_organization_path(record.organization),
            class: "btn btn-secondary"
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record)
    "#{record.user.full_name || record.user.email} @ #{record.organization.name} (#{record.org_role})"
  end

  # Customize the default sort column and direction.
  def self.default_sort_column = "created_at"
  def self.default_sort_direction = "desc"
end
