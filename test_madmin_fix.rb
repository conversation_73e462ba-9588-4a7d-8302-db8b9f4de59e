#!/usr/bin/env ruby

# Test script to verify madmin integration is working
require_relative 'config/environment'

puts "Testing Madmin Integration Fix"
puts "=" * 40

# Test 1: Check if User.full_name method works
puts "\n1. Testing User.full_name method:"
user = User.first
if user
  puts "   User: #{user.email}"
  puts "   First Name: #{user.first_name}"
  puts "   Last Name: #{user.last_name}"
  puts "   Full Name: #{user.full_name}"
  puts "   ✅ User.full_name method works correctly"
else
  puts "   ❌ No users found in database"
  exit 1
end

# Test 2: Check if UserResource.display_name works
puts "\n2. Testing UserResource.display_name method:"
begin
  display_name = UserResource.display_name(user)
  puts "   Display Name: #{display_name}"
  puts "   ✅ UserResource.display_name method works correctly"
rescue => e
  puts "   ❌ UserResource.display_name failed: #{e.message}"
  exit 1
end

# Test 3: Check if madmin routes are accessible
puts "\n3. Testing madmin routes:"
begin
  # Check if the madmin root path helper exists
  if Rails.application.routes.url_helpers.respond_to?(:super_admin_madmin_root_path)
    madmin_path = Rails.application.routes.url_helpers.super_admin_madmin_root_path
    puts "   Madmin root path: #{madmin_path}"
    puts "   ✅ Madmin routes are properly configured"
  else
    puts "   ❌ Madmin root path helper not found"
    exit 1
  end
rescue => e
  puts "   ❌ Madmin routes test failed: #{e.message}"
  exit 1
end

# Test 4: Check if super admin authentication works
puts "\n4. Testing super admin authentication:"
superadmin = User.joins(:roles).where(roles: { name: 'superadmin' }).first
if superadmin
  puts "   Super admin found: #{superadmin.email}"
  puts "   Super admin full name: #{superadmin.full_name}"
  puts "   ✅ Super admin user exists and full_name works"
else
  puts "   ❌ No super admin user found"
  exit 1
end

puts "\n" + "=" * 40
puts "✅ All tests passed! Madmin integration is working correctly."
puts "The NoMethodError for 'full_name' has been fixed."
puts "You can now access the madmin interface at /super_admin/madmin"
