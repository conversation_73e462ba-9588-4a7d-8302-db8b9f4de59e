# Below are the routes for madmin
namespace :madmin do
  namespace :action_text do
    resources :encrypted_rich_texts
  end
  namespace :action_text do
    resources :rich_texts
  end
  namespace :active_storage do
    resources :variant_records
  end
  namespace :pay do
    resources :payment_methods
  end
  namespace :pay do
    namespace :fake_processor do
      resources :payment_methods
    end
  end
  namespace :pay do
    namespace :fake_processor do
      resources :subscriptions
    end
  end
  namespace :pay do
    namespace :lemon_squeezy do
      resources :charges
    end
  end
  namespace :pay do
    namespace :lemon_squeezy do
      resources :customers
    end
  end
  namespace :pay do
    resources :customers
  end
  namespace :pay do
    namespace :lemon_squeezy do
      resources :payment_methods
    end
  end
  namespace :pay do
    namespace :lemon_squeezy do
      resources :subscriptions
    end
  end
  namespace :pay do
    namespace :paddle_billing do
      resources :charges
    end
  end
  namespace :pay do
    namespace :paddle_billing do
      resources :customers
    end
  end
  namespace :pay do
    resources :merchants
  end
  namespace :pay do
    resources :charges
  end
  namespace :pay do
    namespace :paddle_billing do
      resources :payment_methods
    end
  end
  namespace :active_storage do
    resources :attachments
  end
  namespace :active_storage do
    resources :blobs
  end
  resources :job_applications
  namespace :pay do
    resources :webhooks
  end
  namespace :pay do
    namespace :braintree do
      resources :charges
    end
  end
  namespace :pay do
    namespace :braintree do
      resources :customers
    end
  end
  namespace :pay do
    namespace :braintree do
      resources :payment_methods
    end
  end
  namespace :pay do
    namespace :braintree do
      resources :subscriptions
    end
  end
  namespace :pay do
    namespace :fake_processor do
      resources :charges
    end
  end
  namespace :pay do
    namespace :fake_processor do
      resources :customers
    end
  end
  namespace :pay do
    namespace :fake_processor do
      resources :merchants
    end
  end
  namespace :pay do
    namespace :paddle_billing do
      resources :subscriptions
    end
  end
  namespace :pay do
    namespace :paddle_classic do
      resources :charges
    end
  end
  namespace :pay do
    namespace :paddle_classic do
      resources :customers
    end
  end
  namespace :pay do
    namespace :paddle_classic do
      resources :payment_methods
    end
  end
  namespace :pay do
    namespace :paddle_classic do
      resources :subscriptions
    end
  end
  namespace :pay do
    namespace :stripe do
      resources :charges
    end
  end
  namespace :pay do
    namespace :stripe do
      resources :customers
    end
  end
  resources :job_invitations
  resources :messages
  namespace :pay do
    namespace :stripe do
      resources :merchants
    end
  end
  resources :organizations
  namespace :pay do
    namespace :stripe do
      resources :payment_methods
    end
  end
  namespace :pay do
    namespace :stripe do
      resources :subscriptions
    end
  end
  resources :organization_memberships
  resources :roles
  resources :saved_jobs
  namespace :pay do
    resources :subscriptions
  end
  resources :sessions
  resources :talent_bookmarks
  resources :talent_notes
  resources :talent_profiles
  resources :users
  resources :chat_requests
  resources :conversations
  resources :conversation_participants
  resources :impersonation_logs
  resources :jobs
  resources :user_roles
  root to: "dashboard#show"
end
